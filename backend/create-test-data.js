const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('🔄 Criando dados de teste...');

    // 1. Criar usuário de teste
    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash('password', 10);

    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword,
      },
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: '<PERSON><PERSON><PERSON><PERSON>e',
        isActive: true,
      },
    });
    console.log('✅ Usuário criado:', user.email);

    // 2. Criar categorias
    const categories = await Promise.all([
      prisma.category.upsert({
        where: { id: 'cat-1' },
        update: {},
        create: {
          id: 'cat-1',
          name: '<PERSON><PERSON><PERSON><PERSON>',
          color: '#FF6B6B',
        },
      }),
      prisma.category.upsert({
        where: { id: 'cat-2' },
        update: {},
        create: {
          id: 'cat-2',
          name: '<PERSON><PERSON>',
          color: '#4ECDC4',
        },
      }),
      prisma.category.upsert({
        where: { id: 'cat-3' },
        update: {},
        create: {
          id: 'cat-3',
          name: 'Lazer',
          color: '#45B7D1',
        },
      }),
    ]);
    console.log('✅ Categorias criadas:', categories.length);

    // 3. Criar contas
    const accounts = await Promise.all([
      prisma.account.upsert({
        where: { id: 'acc-1' },
        update: {},
        create: {
          id: 'acc-1',
          name: 'Conta Corrente',
          type: 'CHECKING',
          currency: 'BRL',
          currentBalance: 5000.00,
          includeInTotal: true,
        },
      }),
      prisma.account.upsert({
        where: { id: 'acc-2' },
        update: {},
        create: {
          id: 'acc-2',
          name: 'Cartão de Crédito',
          type: 'CREDIT_CARD',
          currency: 'BRL',
          currentBalance: -1200.00,
          creditLimit: 5000.00,
          includeInTotal: true,
        },
      }),
      prisma.account.upsert({
        where: { id: 'acc-3' },
        update: {},
        create: {
          id: 'acc-3',
          name: 'Poupança',
          type: 'SAVINGS',
          currency: 'BRL',
          currentBalance: 10000.00,
          includeInTotal: true,
        },
      }),
    ]);
    console.log('✅ Contas criadas:', accounts.length);

    // 4. Criar membros da família
    const familyMembers = await Promise.all([
      prisma.familyMember.upsert({
        where: { id: 'member-1' },
        update: {},
        create: {
          id: 'member-1',
          name: 'João',
          color: '#FF6B6B',
        },
      }),
      prisma.familyMember.upsert({
        where: { id: 'member-2' },
        update: {},
        create: {
          id: 'member-2',
          name: 'Maria',
          color: '#4ECDC4',
        },
      }),
    ]);
    console.log('✅ Membros da família criados:', familyMembers.length);

    // 5. Criar transações
    const transactions = await Promise.all([
      prisma.transaction.upsert({
        where: { id: 'trans-1' },
        update: {},
        create: {
          id: 'trans-1',
          description: 'Supermercado',
          totalAmount: -250.00,
          transactionDate: new Date('2025-06-20'),
          type: 'EXPENSE',
          accountId: 'acc-1',
          categoryId: 'cat-1',
          isFuture: false,
        },
      }),
      prisma.transaction.upsert({
        where: { id: 'trans-2' },
        update: {},
        create: {
          id: 'trans-2',
          description: 'Combustível',
          totalAmount: -120.00,
          transactionDate: new Date('2025-06-21'),
          type: 'EXPENSE',
          accountId: 'acc-2',
          categoryId: 'cat-2',
          isFuture: false,
        },
      }),
      prisma.transaction.upsert({
        where: { id: 'trans-3' },
        update: {},
        create: {
          id: 'trans-3',
          description: 'Cinema',
          totalAmount: -80.00,
          transactionDate: new Date('2025-06-22'),
          type: 'EXPENSE',
          accountId: 'acc-1',
          categoryId: 'cat-3',
          isFuture: false,
        },
      }),
      prisma.transaction.upsert({
        where: { id: 'trans-4' },
        update: {},
        create: {
          id: 'trans-4',
          description: 'Salário',
          totalAmount: 5000.00,
          transactionDate: new Date('2025-06-01'),
          type: 'INCOME',
          accountId: 'acc-1',
          isFuture: false,
        },
      }),
    ]);
    console.log('✅ Transações criadas:', transactions.length);

    // 6. Criar orçamentos
    const budgets = await Promise.all([
      prisma.budget.upsert({
        where: { id: 'budget-1' },
        update: {},
        create: {
          id: 'budget-1',
          plannedAmount: 800.00,
          month: 6,
          year: 2025,
          categoryId: 'cat-1',
        },
      }),
      prisma.budget.upsert({
        where: { id: 'budget-2' },
        update: {},
        create: {
          id: 'budget-2',
          plannedAmount: 400.00,
          month: 6,
          year: 2025,
          categoryId: 'cat-2',
        },
      }),
    ]);
    console.log('✅ Orçamentos criados:', budgets.length);

    // 7. Criar metas
    const goals = await Promise.all([
      prisma.goal.upsert({
        where: { id: 'goal-1' },
        update: {},
        create: {
          id: 'goal-1',
          name: 'Viagem de Férias',
          targetAmount: 8000.00,
          currentAmount: 2500.00,
          targetDate: new Date('2025-12-31'),
          goalType: 'ACCUMULATION',
          initialAmount: 0.00,
        },
      }),
      prisma.goal.upsert({
        where: { id: 'goal-2' },
        update: {},
        create: {
          id: 'goal-2',
          name: 'Emergência',
          targetAmount: 15000.00,
          currentAmount: 10000.00,
          targetDate: new Date('2026-06-30'),
          goalType: 'ACCUMULATION',
          initialAmount: 5000.00,
        },
      }),
    ]);
    console.log('✅ Metas criadas:', goals.length);

    console.log('🎉 Dados de teste criados com sucesso!');
    console.log('📊 Agora o dashboard deve exibir dados.');

  } catch (error) {
    console.error('❌ Erro ao criar dados de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
