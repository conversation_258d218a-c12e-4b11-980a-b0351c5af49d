import { Request, Response, NextFunction } from 'express';
import { DashboardFiltersSchema } from '../schemas/dashboard.schemas';
import { z } from 'zod';

/**
 * Middleware to validate dashboard filters and transform query parameters
 */
export const validateDashboardFilters = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Transform comma-separated strings to arrays
    const transformedQuery = { ...req.query };
    
    // Handle array parameters that come as comma-separated strings
    const arrayParams = ['accountIds', 'categoryIds', 'familyMemberIds', 'currencies', 'tagIds', 'budgetIds'];
    
    arrayParams.forEach(param => {
      if (transformedQuery[param] && typeof transformedQuery[param] === 'string') {
        transformedQuery[param] = (transformedQuery[param] as string).split(',').filter(Boolean);
      }
    });
    
    // Handle date range object construction
    if (transformedQuery.startDate || transformedQuery.endDate) {
      transformedQuery.dateRange = {
        startDate: transformedQuery.startDate,
        endDate: transformedQuery.endDate
      };
      delete transformedQuery.startDate;
      delete transformedQuery.endDate;
    }
    
    // Handle period object construction
    if (transformedQuery.month || transformedQuery.year) {
      (transformedQuery as any).period = {
        month: transformedQuery.month ? parseInt(transformedQuery.month as string) : undefined,
        year: transformedQuery.year ? parseInt(transformedQuery.year as string) : undefined
      };
      delete transformedQuery.month;
      delete transformedQuery.year;
    }
    
    // Validate the transformed query
    const validatedFilters = DashboardFiltersSchema.parse(transformedQuery);
    
    // Replace req.query with validated filters
    req.query = validatedFilters as any;
    
    next();
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        error: 'Invalid dashboard filters',
        details: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          received: (err as any).input
        }))
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Filter validation error',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
};

/**
 * Middleware to add default filters for common dashboard queries
 */
export const addDefaultFilters = (req: Request, res: Response, next: NextFunction) => {
  // No default filters - show all data when no filters are specified
  // This allows users to see all their data by default
  next();
};

/**
 * Middleware to log dashboard API usage for analytics
 */
export const logDashboardUsage = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Store original res.json to intercept response
  const originalJson = res.json;
  
  res.json = function(body: any) {
    const duration = Date.now() - startTime;
    const endpoint = req.path;
    const filters = req.query;
    const success = res.statusCode >= 200 && res.statusCode < 300;
    
    // Log dashboard usage (could be sent to analytics service)
    console.log(`Dashboard API: ${req.method} ${endpoint} - ${res.statusCode} (${duration}ms)`, {
      filters: Object.keys(filters).length > 0 ? filters : 'none',
      success,
      duration,
      timestamp: new Date().toISOString()
    });
    
    // Log slow requests
    if (duration > 2000) {
      console.warn(`🐌 Slow dashboard request: ${endpoint} took ${duration}ms`);
    }
    
    return originalJson.call(this, body);
  };
  
  next();
};

/**
 * Middleware to add response headers for dashboard caching
 */
export const addCacheHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Set cache headers based on endpoint
  const endpoint = req.path;
  
  if (endpoint.includes('/overview')) {
    // Overview data changes frequently
    res.set('Cache-Control', 'private, max-age=300'); // 5 minutes
  } else if (endpoint.includes('/net-worth') || endpoint.includes('/goal-progress')) {
    // Net worth and goals change less frequently
    res.set('Cache-Control', 'private, max-age=600'); // 10 minutes
  } else if (endpoint.includes('/expenses-by-category') || endpoint.includes('/expenses-by-member')) {
    // Historical analysis can be cached longer
    res.set('Cache-Control', 'private, max-age=900'); // 15 minutes
  } else {
    // Default cache for other endpoints
    res.set('Cache-Control', 'private, max-age=300'); // 5 minutes
  }
  
  // Add ETag for conditional requests
  res.set('ETag', `"dashboard-${Date.now()}"`);
  
  next();
};

/**
 * Middleware to handle dashboard rate limiting
 */
export const dashboardRateLimit = (req: Request, res: Response, next: NextFunction) => {
  // Simple in-memory rate limiting (in production, use Redis)
  const clientId = req.ip || 'unknown';
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute window
  const maxRequests = process.env.NODE_ENV === 'development' ? 500 : 100; // Higher limit for development
  
  // This would be stored in Redis in production
  if (!global.dashboardRateLimit) {
    global.dashboardRateLimit = new Map();
  }
  
  const clientData = global.dashboardRateLimit.get(clientId) || { count: 0, resetTime: now + windowMs };
  
  // Reset if window has passed
  if (now > clientData.resetTime) {
    clientData.count = 0;
    clientData.resetTime = now + windowMs;
  }
  
  clientData.count++;
  global.dashboardRateLimit.set(clientId, clientData);
  
  // Check if rate limit exceeded
  if (clientData.count > maxRequests) {
    res.status(429).json({
      success: false,
      error: 'Rate limit exceeded',
      details: `Maximum ${maxRequests} requests per minute allowed`,
      retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
    });
    return;
  }
  
  // Add rate limit headers
  res.set({
    'X-RateLimit-Limit': maxRequests.toString(),
    'X-RateLimit-Remaining': (maxRequests - clientData.count).toString(),
    'X-RateLimit-Reset': new Date(clientData.resetTime).toISOString()
  });
  
  next();
};

/**
 * Combined middleware for dashboard routes
 */
export const dashboardMiddleware = [
  dashboardRateLimit,
  addDefaultFilters,
  validateDashboardFilters,
  addCacheHeaders,
  logDashboardUsage
];

// Extend global type for rate limiting
declare global {
  var dashboardRateLimit: Map<string, { count: number; resetTime: number }> | undefined;
}
