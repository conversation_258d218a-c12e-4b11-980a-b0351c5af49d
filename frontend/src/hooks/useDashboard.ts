import { useQuery } from '@tanstack/react-query'
import { dashboardApi } from '@/lib/api'
import type { DashboardFilters } from '@/types/dashboard.types'

// Query keys for dashboard data
export const dashboardKeys = {
  all: ['dashboard'] as const,
  overview: (filters?: DashboardFilters) => [...dashboardKeys.all, 'overview', filters] as const,
  accountBalances: (filters?: DashboardFilters) => [...dashboardKeys.all, 'account-balances', filters] as const,
  netWorth: (filters?: DashboardFilters) => [...dashboardKeys.all, 'net-worth', filters] as const,
  creditCardUsage: (filters?: DashboardFilters) => [...dashboardKeys.all, 'credit-card-usage', filters] as const,
  expensesByCategory: (filters?: DashboardFilters) => [...dashboardKeys.all, 'expenses-by-category', filters] as const,
  expensesByMember: (filters?: DashboardFilters) => [...dashboardKeys.all, 'expenses-by-member', filters] as const,
  budgetComparison: (filters?: DashboardFilters) => [...dashboardKeys.all, 'budget-comparison', filters] as const,
  goalProgress: (filters?: DashboardFilters) => [...dashboardKeys.all, 'goal-progress', filters] as const,
  performanceMetrics: () => [...dashboardKeys.all, 'performance-metrics'] as const,
}

/**
 * Hook to fetch complete dashboard overview
 */
export function useDashboardOverview(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.overview(filters),
    queryFn: () => dashboardApi.getOverview(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch account balances aggregation
 */
export function useDashboardAccountBalances(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.accountBalances(filters),
    queryFn: () => dashboardApi.getAccountBalances(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch net worth data and history
 */
export function useDashboardNetWorth(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.netWorth(filters),
    queryFn: () => dashboardApi.getNetWorth(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to fetch credit card usage data
 */
export function useDashboardCreditCardUsage(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.creditCardUsage(filters),
    queryFn: () => dashboardApi.getCreditCardUsage(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch expenses by category
 */
export function useDashboardExpensesByCategory(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.expensesByCategory(filters),
    queryFn: () => dashboardApi.getExpensesByCategory(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch expenses by family member
 */
export function useDashboardExpensesByMember(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.expensesByMember(filters),
    queryFn: () => dashboardApi.getExpensesByMember(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch budget comparison data
 */
export function useDashboardBudgetComparison(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.budgetComparison(filters),
    queryFn: () => dashboardApi.getBudgetComparison(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch goal progress data
 */
export function useDashboardGoalProgress(filters?: DashboardFilters) {
  return useQuery({
    queryKey: dashboardKeys.goalProgress(filters),
    queryFn: () => dashboardApi.getGoalProgress(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch performance metrics
 */
export function useDashboardPerformanceMetrics() {
  return useQuery({
    queryKey: dashboardKeys.performanceMetrics(),
    queryFn: () => dashboardApi.getPerformanceMetrics(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  })
}
