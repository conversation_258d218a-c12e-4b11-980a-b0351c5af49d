import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { goalsApi, milestonesApi } from '@/lib/api/goals.api'
import { progressHistoryKeys } from './useProgressHistory'
import type {
  CreateGoalData,
  UpdateGoalData,
  UpdateProgressData,
  CreateMilestoneData,
  UpdateMilestoneData,
  GoalFilters,
  MilestoneFilters,
} from '@/types/goal.types'

// Query keys
export const goalKeys = {
  all: ['goals'] as const,
  lists: () => [...goalKeys.all, 'list'] as const,
  list: (filters: GoalFilters) => [...goalKeys.lists(), filters] as const,
  details: () => [...goalKeys.all, 'detail'] as const,
  detail: (id: string) => [...goalKeys.details(), id] as const,
  summary: () => [...goalKeys.all, 'summary'] as const,
}

export const milestoneKeys = {
  all: ['milestones'] as const,
  lists: () => [...milestoneKeys.all, 'list'] as const,
  list: (filters: MilestoneFilters) => [...milestoneKeys.lists(), filters] as const,
  details: () => [...milestoneKeys.all, 'detail'] as const,
  detail: (id: string) => [...milestoneKeys.details(), id] as const,
  byGoal: (goalId: string) => [...milestoneKeys.all, 'goal', goalId] as const,
}

/**
 * Hook to fetch all financial goals with filters
 */
export function useGoals(filters: GoalFilters = {}) {
  return useQuery({
    queryKey: goalKeys.list(filters),
    queryFn: () => goalsApi.getAll(filters),
    staleTime: 0, // Always fresh data
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch a single financial goal by ID
 */
export function useGoal(id: string, includeMilestones = true) {
  return useQuery({
    queryKey: goalKeys.detail(id),
    queryFn: () => goalsApi.getById(id, includeMilestones),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch goals summary/statistics
 */
export function useGoalsSummary() {
  return useQuery({
    queryKey: goalKeys.summary(),
    queryFn: () => goalsApi.getSummary(),
    staleTime: 0, // Always fresh data
  })
}

/**
 * Hook to fetch active goals
 */
export function useActiveGoals(filters: Omit<GoalFilters, 'status'> = {}) {
  return useGoals({ ...filters, status: 'active' })
}

/**
 * Hook to fetch completed goals
 */
export function useCompletedGoals(filters: Omit<GoalFilters, 'status'> = {}) {
  return useGoals({ ...filters, status: 'completed' })
}

/**
 * Hook to fetch overdue goals
 */
export function useOverdueGoals(filters: Omit<GoalFilters, 'status'> = {}) {
  return useGoals({ ...filters, status: 'overdue' })
}

/**
 * Hook to create a new financial goal
 */
export function useCreateGoal() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateGoalData) => goalsApi.create(data),
    onSuccess: (response) => {
      // Invalidate and refetch goals lists - more aggressive invalidation
      queryClient.invalidateQueries({ queryKey: goalKeys.all })
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })
      queryClient.invalidateQueries({ queryKey: goalKeys.summary() })

      // Add the new goal to the cache
      queryClient.setQueryData(goalKeys.detail(response.data.id), response)

      // Force refetch of the main goals list
      queryClient.refetchQueries({ queryKey: goalKeys.list({}) })

      toast.success('Meta financeira criada com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao criar meta financeira')
    },
  })
}

/**
 * Hook to update an existing financial goal
 */
export function useUpdateGoal() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateGoalData }) =>
      goalsApi.update(id, data),
    onSuccess: (response) => {
      // Update the goal in the cache
      queryClient.setQueryData(goalKeys.detail(response.data.id), response)
      
      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })
      queryClient.invalidateQueries({ queryKey: goalKeys.summary() })
      
      toast.success('Meta financeira atualizada com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao atualizar meta financeira')
    },
  })
}

/**
 * Hook to update goal progress manually
 */
export function useUpdateGoalProgress() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProgressData }) =>
      goalsApi.updateProgress(id, data),
    onSuccess: (response) => {
      // Update the goal in the cache
      queryClient.setQueryData(goalKeys.detail(response.data.id), response)

      // Invalidate lists and summary to reflect progress changes
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })
      queryClient.invalidateQueries({ queryKey: goalKeys.summary() })

      // Invalidate progress history queries to update the history component
      queryClient.invalidateQueries({ queryKey: progressHistoryKeys.all })
      queryClient.invalidateQueries({ queryKey: progressHistoryKeys.byGoal(response.data.id) })

      toast.success('Progresso da meta atualizado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao atualizar progresso da meta')
    },
  })
}

/**
 * Hook to delete a financial goal
 */
export function useDeleteGoal() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => goalsApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: goalKeys.detail(id) })

      // Invalidate lists and summary
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })
      queryClient.invalidateQueries({ queryKey: goalKeys.summary() })

      // Also invalidate milestones for this goal
      queryClient.invalidateQueries({ queryKey: milestoneKeys.byGoal(id) })

      // Invalidate progress history queries for this goal
      queryClient.invalidateQueries({ queryKey: progressHistoryKeys.all })
      queryClient.invalidateQueries({ queryKey: progressHistoryKeys.byGoal(id) })

      toast.success('Meta financeira excluída com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao excluir meta financeira')
    },
  })
}

/**
 * Hook to fetch milestones for a specific goal
 */
export function useMilestones(goalId: string, filters: Omit<MilestoneFilters, 'goalId'> = {}) {
  return useQuery({
    queryKey: milestoneKeys.byGoal(goalId),
    queryFn: () => milestonesApi.getByGoal(goalId, filters),
    enabled: !!goalId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch a single milestone by ID
 */
export function useMilestone(id: string) {
  return useQuery({
    queryKey: milestoneKeys.detail(id),
    queryFn: () => milestonesApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to create a new milestone
 */
export function useCreateMilestone() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ goalId, data }: { goalId: string; data: CreateMilestoneData }) =>
      milestonesApi.create(goalId, data),
    onSuccess: (response, { goalId }) => {
      // Invalidate milestones for this goal
      queryClient.invalidateQueries({ queryKey: milestoneKeys.byGoal(goalId) })

      // Add the new milestone to the cache
      queryClient.setQueryData(milestoneKeys.detail(response.data.id), response)

      // Invalidate the goal detail to refresh milestones
      queryClient.invalidateQueries({ queryKey: goalKeys.detail(goalId) })

      // Invalidate goals lists to refresh milestones in the cards
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })

      toast.success('Marco criado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao criar marco')
    },
  })
}

/**
 * Hook to update an existing milestone
 */
export function useUpdateMilestone() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMilestoneData }) =>
      milestonesApi.update(id, data),
    onSuccess: (response) => {
      // Update the milestone in the cache
      queryClient.setQueryData(milestoneKeys.detail(response.data.id), response)

      // Invalidate milestones for the goal
      queryClient.invalidateQueries({ queryKey: milestoneKeys.byGoal(response.data.goalId) })

      // Invalidate the goal detail to refresh milestones
      queryClient.invalidateQueries({ queryKey: goalKeys.detail(response.data.goalId) })

      // Invalidate goals lists to refresh milestones in the cards
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })

      toast.success('Marco atualizado com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao atualizar marco')
    },
  })
}

/**
 * Hook to delete a milestone
 */
export function useDeleteMilestone() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => milestonesApi.delete(id),
    onMutate: async (id) => {
      // Get the milestone to know which goal it belongs to
      const milestone = queryClient.getQueryData(milestoneKeys.detail(id)) as any
      return { milestone }
    },
    onSuccess: (_, id, context) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: milestoneKeys.detail(id) })

      // If we have the milestone data, invalidate the goal's milestones
      if (context?.milestone?.goalId) {
        queryClient.invalidateQueries({ queryKey: milestoneKeys.byGoal(context.milestone.goalId) })
        queryClient.invalidateQueries({ queryKey: goalKeys.detail(context.milestone.goalId) })

        // Invalidate goals lists to refresh milestones in the cards
        queryClient.invalidateQueries({ queryKey: goalKeys.lists() })
      }

      toast.success('Marco excluído com sucesso!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao excluir marco')
    },
  })
}

/**
 * Hook to mark milestone as completed
 */
export function useCompleteMilestone() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => milestonesApi.markCompleted(id),
    onSuccess: (response) => {
      // Update the milestone in the cache
      queryClient.setQueryData(milestoneKeys.detail(response.data.id), response)

      // Invalidate milestones for the goal
      queryClient.invalidateQueries({ queryKey: milestoneKeys.byGoal(response.data.goalId) })

      // Invalidate the goal detail to refresh milestones
      queryClient.invalidateQueries({ queryKey: goalKeys.detail(response.data.goalId) })

      // Invalidate goals lists to refresh milestones in the cards
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })

      toast.success('Marco marcado como concluído!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao marcar marco como concluído')
    },
  })
}

/**
 * Hook to mark milestone as pending
 */
export function usePendingMilestone() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => milestonesApi.markPending(id),
    onSuccess: (response) => {
      // Update the milestone in the cache
      queryClient.setQueryData(milestoneKeys.detail(response.data.id), response)

      // Invalidate milestones for the goal
      queryClient.invalidateQueries({ queryKey: milestoneKeys.byGoal(response.data.goalId) })

      // Invalidate the goal detail to refresh milestones
      queryClient.invalidateQueries({ queryKey: goalKeys.detail(response.data.goalId) })

      // Invalidate goals lists to refresh milestones in the cards
      queryClient.invalidateQueries({ queryKey: goalKeys.lists() })

      toast.success('Marco marcado como pendente!')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Erro ao marcar marco como pendente')
    },
  })
}
