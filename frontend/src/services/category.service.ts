import { api } from '@/lib/api'
import type {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryFilters,
  PaginatedCategoriesResponse,
  CategoryStats,
  CategoryResponse,
  CategoryTreeNode,
  BulkCategoryOperation,
  BulkCategoryResponse,
  CategoryHierarchyValidation,
} from '@/types/category.types'

const BASE_URL = '/categories'

export const categoryService = {
  /**
   * Get all categories with optional filters
   */
  async getAll(filters: CategoryFilters = {}): Promise<PaginatedCategoriesResponse> {
    const params = new URLSearchParams()

    if (filters.name) params.append('name', filters.name)
    if (filters.parentId !== undefined) {
      params.append('parentId', filters.parentId || '')
    }
    if (filters.includeChildren !== undefined) {
      params.append('includeChildren', filters.includeChildren.toString())
    }
    if (filters.onlyParents !== undefined) {
      params.append('onlyParents', filters.onlyParents.toString())
    }
    if (filters.onlyChildren !== undefined) {
      params.append('onlyChildren', filters.onlyChildren.toString())
    }
    if (filters.includeArchived !== undefined) {
      params.append('includeArchived', filters.includeArchived.toString())
    }
    if (filters.includeDeleted !== undefined) {
      params.append('includeDeleted', filters.includeDeleted.toString())
    }
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())

    const response = await api.get<{
      success: boolean
      data: Category[]
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
        hasNext: boolean
        hasPrev: boolean
      }
      message?: string
    }>(`${BASE_URL}?${params.toString()}`)

    if (!response.data.success) {
      throw new Error('Erro ao buscar categorias')
    }

    // Return the data in the expected format
    return {
      data: response.data.data,
      pagination: response.data.pagination
    }
  },

  /**
   * Get category tree for hierarchical display
   */
  async getCategoryTree(includeArchived = false): Promise<CategoryTreeNode[]> {
    const params = new URLSearchParams()
    if (includeArchived) params.append('includeArchived', 'true')

    const response = await api.get<{
      success: boolean
      data: CategoryTreeNode[]
      message?: string
    }>(`${BASE_URL}/tree?${params.toString()}`)

    if (!response.data.success) {
      throw new Error('Erro ao buscar árvore de categorias')
    }

    return response.data.data
  },

  /**
   * Get a single category by ID
   */
  async getById(id: string, includeArchived = false): Promise<Category> {
    const params = new URLSearchParams()
    if (includeArchived) params.append('includeArchived', 'true')

    const response = await api.get<CategoryResponse>(
      `${BASE_URL}/${id}?${params.toString()}`
    )

    if (!response.data.success) {
      throw new Error('Erro ao buscar categoria')
    }

    return response.data.data
  },

  /**
   * Create a new category
   */
  async create(data: CreateCategoryRequest): Promise<Category> {
    const response = await api.post<CategoryResponse>(BASE_URL, data)

    if (!response.data.success) {
      throw new Error('Erro ao criar categoria')
    }

    return response.data.data
  },

  /**
   * Update an existing category
   */
  async update(id: string, data: UpdateCategoryRequest): Promise<Category> {
    const response = await api.put<CategoryResponse>(`${BASE_URL}/${id}`, data)

    if (!response.data.success) {
      throw new Error('Erro ao atualizar categoria')
    }

    return response.data.data
  },

  /**
   * Archive or unarchive a category
   */
  async archive(id: string, archived: boolean): Promise<Category> {
    const response = await api.patch<CategoryResponse>(`${BASE_URL}/${id}/archive`, {
      archived
    })

    if (!response.data.success) {
      throw new Error(`Erro ao ${archived ? 'arquivar' : 'restaurar'} categoria`)
    }

    return response.data.data
  },

  /**
   * Restore an archived category
   */
  async restore(id: string): Promise<Category> {
    return this.archive(id, false)
  },

  /**
   * Permanently delete a category
   */
  async delete(id: string): Promise<void> {
    const response = await api.delete<{ success: boolean; message?: string }>(`${BASE_URL}/${id}`)

    if (!response.data.success) {
      throw new Error(response.data.message || 'Erro ao excluir categoria permanentemente')
    }
  },

  /**
   * Get categories statistics
   */
  async getStats(): Promise<CategoryStats> {
    const response = await api.get<{ success: boolean; data: CategoryStats }>(`${BASE_URL}/stats`)
    
    if (!response.data.success) {
      throw new Error('Erro ao buscar estatísticas das categorias')
    }
    
    return response.data.data
  },

  /**
   * Validate category hierarchy
   */
  async validateHierarchy(parentId: string, categoryId?: string): Promise<CategoryHierarchyValidation> {
    const params = new URLSearchParams()
    params.append('parentId', parentId)
    if (categoryId) params.append('categoryId', categoryId)

    const response = await api.get<{ 
      success: boolean 
      data: CategoryHierarchyValidation 
    }>(`${BASE_URL}/validate-hierarchy?${params.toString()}`)
    
    if (!response.data.success) {
      throw new Error('Erro ao validar hierarquia da categoria')
    }
    
    return response.data.data
  },

  /**
   * Perform bulk operations on categories
   */
  async bulkOperation(operation: BulkCategoryOperation): Promise<BulkCategoryResponse> {
    const response = await api.post<BulkCategoryResponse>(`${BASE_URL}/bulk`, operation)
    
    if (!response.data.success) {
      throw new Error('Erro ao executar operação em lote')
    }
    
    return response.data
  },

  /**
   * Get categories for selection (simplified format)
   */
  async getForSelection(excludeId?: string): Promise<Array<{
    value: string
    label: string
    level: number
    disabled?: boolean
  }>> {
    const params = new URLSearchParams()
    params.append('onlyActive', 'true')
    if (excludeId) params.append('excludeId', excludeId)

    const response = await api.get<{
      success: boolean
      data: Array<{
        value: string
        label: string
        level: number
        disabled?: boolean
      }>
    }>(`${BASE_URL}/selection?${params.toString()}`)

    if (!response.data.success) {
      throw new Error('Erro ao buscar categorias para seleção')
    }

    // Return the data as-is since it already matches the expected format
    return response.data.data
  },

  /**
   * Search categories by name
   */
  async search(query: string, limit = 10): Promise<Category[]> {
    const params = new URLSearchParams()
    params.append('q', query)
    params.append('limit', limit.toString())

    const response = await api.get<{
      success: boolean
      data: Category[]
    }>(`${BASE_URL}/search?${params.toString()}`)

    if (!response.data.success) {
      throw new Error('Erro ao buscar categorias')
    }

    return response.data.data
  },
}
