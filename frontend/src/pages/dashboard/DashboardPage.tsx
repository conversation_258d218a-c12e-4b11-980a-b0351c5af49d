import { useState } from 'react'
import { LayoutDashboard, Filter } from 'lucide-react'
import { useDashboardOverview } from '@/hooks/useDashboard'
import { DashboardFilters } from '@/components/dashboard/DashboardFilters'
import { DashboardStatsCards } from '@/components/dashboard/DashboardStatsCards'
import { DashboardCharts } from '@/components/dashboard/DashboardCharts'
import { DashboardInsights } from '@/components/dashboard/DashboardInsights'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import type { DashboardFilters as DashboardFiltersType } from '@/types/dashboard.types'

export function DashboardPage() {
  // Initialize without filters to show all-time data by default
  const [filters, setFilters] = useState<DashboardFiltersType>({})
  const [isFiltersOpen, setIsFiltersOpen] = useState(false)

  const { data: overview, isLoading, error } = useDashboardOverview(filters)

  const handleFiltersChange = (newFilters: DashboardFiltersType) => {
    setFilters(newFilters)
  }

  const handleClearFilters = () => {
    setFilters({})
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="glass-deep p-6 rounded-2xl">
          <h1 className="text-4xl font-bold text-gradient-deep">Dashboard</h1>
          <p className="text-lg text-muted-foreground mt-2">Visão geral das suas finanças</p>
        </div>

        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-lg text-destructive mb-2">Erro ao carregar dados do dashboard</p>
            <p className="text-sm text-muted-foreground">
              {error instanceof Error ? error.message : 'Erro desconhecido'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="glass-deep p-6 rounded-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-gradient-deep flex items-center gap-3">
              <LayoutDashboard className="h-10 w-10" />
              Dashboard
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Visão geral das suas finanças pessoais
            </p>
          </div>

          {/* Filters Toggle */}
          <Collapsible open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
            <CollapsibleTrigger asChild>
              <Button variant="outline" className="glass-deep">
                <Filter className="h-4 w-4 mr-2" />
                Filtros
              </Button>
            </CollapsibleTrigger>
          </Collapsible>
        </div>

        {/* Filters Panel */}
        <Collapsible open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
          <CollapsibleContent className="mt-6">
            <DashboardFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={handleClearFilters}
              isLoading={isLoading}
            />
          </CollapsibleContent>
        </Collapsible>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      )}

      {/* Dashboard Content */}
      {!isLoading && (
        <>
          {/* Stats Cards */}
          <DashboardStatsCards
            overview={overview?.data}
            isLoading={isLoading}
          />

          {/* Charts */}
          <DashboardCharts
            overview={overview?.data}
            isLoading={isLoading}
          />

          {/* Insights and Alerts */}
          <DashboardInsights
            overview={overview?.data}
            isLoading={isLoading}
          />
        </>
      )}
    </div>
  )

}
