// Dashboard filter types
export interface DashboardFilters {
  // Account filters
  accountIds?: string[]
  
  // Category filters
  categoryIds?: string[]
  
  // Family member filters
  familyMemberIds?: string[]
  
  // Currency filters
  currencies?: string[]
  
  // Date range filters
  startDate?: string
  endDate?: string
  
  // Period filters (alternative to date range)
  month?: number // 1-12
  year?: number
  
  // Tag filters
  tagIds?: string[]
  
  // Budget filters
  budgetIds?: string[]
}

// Account balance aggregation types
export interface AccountBalanceAggregation {
  totalBalance: number
  balanceByType: {
    CHECKING: number
    SAVINGS: number
    CREDIT_CARD: number
    INVESTMENT: number
    CASH: number
    ASSETS: number
  }
  balanceByCurrency: Record<string, number>
  accounts: Array<{
    id: string
    name: string
    type: string
    balance: number
    currency: string
    exchangeRate: number
    balanceInBRL: number
  }>
}

// Net worth aggregation types
export interface NetWorthAggregation {
  currentNetWorth: number
  assets: {
    total: number
    checking: number
    savings: number
    investment: number
    cash: number
    other: number
  }
  liabilities: {
    total: number
    creditCards: number
    loans: number
    other: number
  }
  monthlyHistory: Array<{
    month: string
    netWorth: number
    assets: number
    liabilities: number
    change: number
    changePercentage: number
  }>
}

// Credit card usage types
export interface CreditCardUsage {
  totalUsage: number
  totalLimit: number
  usagePercentage: number
  availableCredit: number
  cardDetails: Array<{
    accountId: string
    accountName: string
    currentBalance: number
    creditLimit: number
    usagePercentage: number
    availableCredit: number
    isNearLimit: boolean
    currency: string
  }>
  alerts: Array<{
    accountId: string
    accountName: string
    alertType: 'NEAR_LIMIT' | 'OVER_LIMIT' | 'HIGH_USAGE'
    message: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH'
  }>
}

// Expense by category types
export interface ExpenseByCategory {
  totalExpenses: number
  categories: Array<{
    categoryId: string
    categoryName: string
    amount: number
    percentage: number
    transactionCount: number
    averageTransactionAmount: number
    color?: string
  }>
}

// Expense by member types
export interface ExpenseByMember {
  totalExpenses: number
  members: Array<{
    memberId: string
    memberName: string
    amount: number
    percentage: number
    transactionCount: number
    averageTransactionAmount: number
    topCategories: Array<{
      categoryId: string
      categoryName: string
      amount: number
      percentage: number
    }>
  }>
}

// Budget comparison types
export interface BudgetComparison {
  totalBudgeted: number
  totalSpent: number
  totalRemaining: number
  overallProgress: number
  categories: Array<{
    categoryId: string
    categoryName: string
    budgeted: number
    spent: number
    remaining: number
    progress: number
    status: 'ON_TRACK' | 'NEAR_LIMIT' | 'OVER_BUDGET'
    projectedSpend?: number
  }>
  alerts: Array<{
    categoryId: string
    categoryName: string
    alertType: 'NEAR_BUDGET' | 'OVER_BUDGET' | 'PROJECTED_OVERSPEND'
    message: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH'
  }>
}

// Goal progress types
export interface GoalProgress {
  totalGoals: number
  activeGoals: number
  completedGoals: number
  goals: Array<{
    goalId: string
    goalName: string
    targetAmount: number
    currentAmount: number
    progress: number
    remaining: number
    targetDate?: string
    daysRemaining?: number
    estimatedCompletionDate?: string
    isOnTrack: boolean
    monthlyRequiredAmount?: number
    status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE'
  }>
  summary: {
    totalTargetAmount: number
    totalCurrentAmount: number
    overallProgress: number
    onTrackGoals: number
    behindScheduleGoals: number
  }
}

// Complete dashboard overview type
export interface DashboardOverview {
  accountBalances: AccountBalanceAggregation
  netWorth: NetWorthAggregation
  creditCardUsage: CreditCardUsage
  expensesByCategory: ExpenseByCategory
  expensesByMember: ExpenseByMember
  budgetComparison: BudgetComparison
  goalProgress: GoalProgress
  generatedAt: string
  filters: DashboardFilters
}

// Performance metrics types
export interface PerformanceMetrics {
  slowQueries: Array<{
    operation: string
    duration: number
    timestamp: string
    filters?: DashboardFilters
  }>
  averageQueryTime: number
  totalQueries: number
  cacheHitRate?: number
}
