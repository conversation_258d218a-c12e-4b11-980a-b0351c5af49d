import axios, { AxiosResponse, AxiosError } from 'axios'
import toast from 'react-hot-toast'
import { API_CONFIG, ERROR_MESSAGES } from './constants'
import type { ApiResponse } from '@/types/global'

// Create axios instance
export const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Only add timestamp for specific endpoints that need fresh data
    if (config.method === 'get' && config.url?.includes('/dashboard/overview')) {
      config.params = {
        ...config.params,
        _t: Date.now(),
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error: AxiosError) => {
    // Handle common errors
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // Unauthorized - let the auth store handle this
          if (window.location.pathname !== '/login') {
            toast.error(ERROR_MESSAGES.UNAUTHORIZED)
          }
          break

        case 403:
          toast.error(ERROR_MESSAGES.FORBIDDEN)
          break

        case 404:
          toast.error(ERROR_MESSAGES.NOT_FOUND)
          break

        case 422:
          // Validation errors
          if (data && typeof data === 'object' && 'details' in data && Array.isArray(data.details)) {
            data.details.forEach((detail: any) => {
              toast.error(detail.message)
            })
          } else {
            toast.error(ERROR_MESSAGES.VALIDATION_ERROR)
          }
          break

        case 429:
          toast.error('Muitas tentativas. Tente novamente em alguns minutos.')
          break

        case 500:
          toast.error(ERROR_MESSAGES.SERVER_ERROR)
          break

        default:
          toast.error(ERROR_MESSAGES.UNKNOWN_ERROR)
      }
    } else if (error.request) {
      // Network error
      toast.error(ERROR_MESSAGES.NETWORK_ERROR)
    } else {
      // Other error
      toast.error(ERROR_MESSAGES.UNKNOWN_ERROR)
    }

    return Promise.reject(error)
  }
)

// Helper function to handle API responses
const handleApiResponse = <T>(promise: Promise<AxiosResponse<ApiResponse<T>>>) => {
  return promise.then((response) => response.data)
}



// API endpoints
export const authApi = {
  login: (email: string, password: string) =>
    handleApiResponse(api.post('/auth/login', { email, password })),

  register: (email: string, password: string, name: string) =>
    handleApiResponse(api.post('/auth/register', { email, password, name })),

  me: () => handleApiResponse(api.get('/auth/profile')),

  logout: () => handleApiResponse(api.post('/auth/logout')),
}

export const accountsApi = {
  getAll: () => handleApiResponse(api.get('/accounts')),

  getById: (id: string) => handleApiResponse(api.get(`/accounts/${id}`)),

  create: (data: any) => handleApiResponse(api.post('/accounts', data)),

  update: (id: string, data: any) => handleApiResponse(api.put(`/accounts/${id}`, data)),

  delete: (id: string) => handleApiResponse(api.delete(`/accounts/${id}`)),

  getBalance: (id: string) => handleApiResponse(api.get(`/accounts/${id}/balance`)),
}

// Family Members API - REMOVED: All components now use familyMemberService
// Migration completed successfully ✅

// Export the enhanced transactions API
export { transactionsApi } from './api/transactions.api'

// Export the goals API
export { goalsApi, milestonesApi } from './api/goals.api'

// Export the budgets API
export { budgetsApi } from './api/budgets.api'

export const futureTransactionsApi = {
  getAll: (params?: any) => api.get('/future-transactions', { params }),

  getProjectedBalance: (params?: any) =>
    api.get('/future-transactions/projected-balance', { params }),

  getStats: () => api.get('/future-transactions/stats'),

  getDueToday: () => api.get('/future-transactions/due-today'),

  update: (id: string, data: any) =>
    api.put(`/future-transactions/${id}`, data),

  cancel: (id: string) => api.delete(`/future-transactions/${id}`),

  process: (data?: any) => api.post('/future-transactions/process', data),
}

// Categories API - REMOVED: All components now use categoryService
// Migration completed successfully ✅

export const recurringTransactionsApi = {
  getAll: (params?: any) => api.get('/recurring-transactions', { params }),

  getById: (id: string) => api.get(`/recurring-transactions/${id}`),

  create: (data: any) => api.post('/recurring-transactions', data),

  update: (id: string, data: any) =>
    api.put(`/recurring-transactions/${id}`, data),

  delete: (id: string) => api.delete(`/recurring-transactions/${id}`),

  toggle: (id: string) => api.patch(`/recurring-transactions/${id}/toggle`),
}

export const dashboardApi = {
  getOverview: (params?: any) => handleApiResponse(api.get('/dashboard/overview', { params })),

  getAccountBalances: (params?: any) =>
    handleApiResponse(api.get('/dashboard/account-balances', { params })),

  getNetWorth: (params?: any) =>
    handleApiResponse(api.get('/dashboard/net-worth', { params })),

  getCreditCardUsage: (params?: any) =>
    handleApiResponse(api.get('/dashboard/credit-card-usage', { params })),

  getExpensesByCategory: (params?: any) =>
    handleApiResponse(api.get('/dashboard/expenses-by-category', { params })),

  getExpensesByMember: (params?: any) =>
    handleApiResponse(api.get('/dashboard/expenses-by-member', { params })),

  getBudgetComparison: (params?: any) =>
    handleApiResponse(api.get('/dashboard/budget-comparison', { params })),

  getGoalProgress: (params?: any) =>
    handleApiResponse(api.get('/dashboard/goal-progress', { params })),

  getTransactionTrends: (params?: any) =>
    handleApiResponse(api.get('/dashboard/transaction-trends', { params })),

  getCategoryBreakdown: (params?: any) =>
    handleApiResponse(api.get('/dashboard/category-breakdown', { params })),

  getRecentTransactions: (params?: any) =>
    handleApiResponse(api.get('/dashboard/recent-transactions', { params })),

  getPerformanceMetrics: () => handleApiResponse(api.get('/dashboard/performance-metrics')),
}

export const cacheApi = {
  getStats: () => api.get('/cache/stats'),

  getHealth: () => api.get('/cache/health'),

  invalidate: (data: any) => api.post('/cache/invalidate', data),
}

export const tagsApi = {
  getAll: (params?: any) => handleApiResponse(api.get('/tags', { params })),

  getById: (id: string, includeArchived = false) =>
    handleApiResponse(api.get(`/tags/${id}`, { params: { includeArchived } })),

  create: (data: any) => handleApiResponse(api.post('/tags', data)),

  update: (id: string, data: any) => handleApiResponse(api.put(`/tags/${id}`, data)),

  delete: (id: string) => handleApiResponse(api.delete(`/tags/${id}`)),

  archive: (id: string, archived: boolean) =>
    handleApiResponse(api.patch(`/tags/${id}/archive`, { archived })),

  getStats: () => handleApiResponse(api.get('/tags/stats')),
}
