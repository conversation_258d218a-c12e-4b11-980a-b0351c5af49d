import { useState, useEffect } from 'react'
import { 
  Filter,
  X,
  Search,
  Calendar,
  DollarSign,
  CreditCard,
  Tag,
  Users,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft
} from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { accountsApi, tagsApi } from '@/lib/api'
import { useCategoriesForForms } from '@/hooks/useCategories'
import { useFamilyMembers } from '@/hooks/useFamilyMembers'
import { TRANSACTION_TYPES, TRANSACTION_TYPE_LABELS, SORT_OPTIONS, SORT_ORDER_OPTIONS } from '@/types/transaction.types'
import type { TransactionFilters } from '@/types/transaction.types'

export interface TransactionsFiltersProps {
  filters: TransactionFilters
  onFiltersChange: (filters: TransactionFilters) => void
  onClearFilters: () => void
  isLoading?: boolean
}

export function TransactionsFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  isLoading = false
}: TransactionsFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Fetch data for filter options
  const { data: accounts } = useQuery({
    queryKey: ['accounts'],
    queryFn: () => accountsApi.getAll(),
  })

  const { data: categories } = useCategoriesForForms()

  const { data: familyMembersData } = useFamilyMembers()

  const { data: tags } = useQuery({
    queryKey: ['tags'],
    queryFn: () => tagsApi.getAll(),
  })

  // Helper function to extract data from different API response formats
  const extractData = (apiResponse: any) => {
    if (!apiResponse) return []

    // If it's already an array, return it
    if (Array.isArray(apiResponse)) return apiResponse

    // If it has a data property that's an array, return that
    if (apiResponse.data && Array.isArray(apiResponse.data)) return apiResponse.data

    // If it has a data property with a data property (nested), return that
    if (apiResponse.data?.data && Array.isArray(apiResponse.data.data)) return apiResponse.data.data

    // Default to empty array
    return []
  }

  // Helper function to remove duplicates by ID
  const removeDuplicatesById = (items: any[]) => {
    const seen = new Set()
    return items.filter((item: any) => {
      if (!item || !item.id || seen.has(item.id)) return false
      seen.add(item.id)
      return true
    })
  }

  // Extract data safely and filter out items without valid IDs or duplicates
  const accountsList = removeDuplicatesById(extractData(accounts))
  const categoriesList = removeDuplicatesById(categories || []) // Categories hook already returns clean array
  const familyMembersList = removeDuplicatesById(familyMembersData?.data || []) // Family members from paginated response
  const tagsList = removeDuplicatesById(extractData(tags))

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'page' || key === 'limit' || key === 'sortBy' || key === 'sortOrder') return false
    return value !== undefined && value !== null && value !== ''
  }).length

  const updateFilter = (key: keyof TransactionFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1 // Reset to first page when filters change
    })
  }

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'INCOME':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'EXPENSE':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'TRANSFER':
        return <ArrowRightLeft className="h-4 w-4 text-blue-600" />
      default:
        return null
    }
  }

  return (
    <Card className="glass-deep shadow-elegant">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-deep shadow-soft">
              <Filter className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gradient">Filtros</CardTitle>
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="mt-1">
                  {activeFiltersCount} filtro{activeFiltersCount > 1 ? 's' : ''} ativo{activeFiltersCount > 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4 mr-2" />
                Limpar
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-muted-foreground hover:text-foreground"
            >
              {isExpanded ? 'Recolher' : 'Expandir'}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Basic Filters - Always Visible */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Search */}
          <div className="space-y-2">
            <Label htmlFor="search" className="text-sm font-medium">
              <Search className="h-4 w-4 inline mr-2" />
              Buscar
            </Label>
            <Input
              id="search"
              placeholder="Descrição da transação..."
              value={filters.description || ''}
              onChange={(e) => updateFilter('description', e.target.value)}
              className="w-full"
            />
          </div>

          {/* Transaction Type */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              <CreditCard className="h-4 w-4 inline mr-2" />
              Tipo
            </Label>
            <Select
              value={filters.type || 'ALL'}
              onValueChange={(value) => updateFilter('type', value === 'ALL' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Todos os tipos" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem key="ALL" value="ALL">Todos os tipos</SelectItem>
                {Object.entries(TRANSACTION_TYPE_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value} textValue={label}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Account */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              <CreditCard className="h-4 w-4 inline mr-2" />
              Conta
            </Label>
            <Select
              value={filters.accountId || 'ALL'}
              onValueChange={(value) => updateFilter('accountId', value === 'ALL' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Todas as contas" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem key="ALL" value="ALL">Todas as contas</SelectItem>
                {accountsList.map((account: any) => (
                  <SelectItem key={account.id} value={account.id}>
                    {account.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              <Tag className="h-4 w-4 inline mr-2" />
              Categoria
            </Label>
            <Select
              value={filters.categoryId || 'ALL'}
              onValueChange={(value) => updateFilter('categoryId', value === 'ALL' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Todas as categorias" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem key="ALL" value="ALL">Todas as categorias</SelectItem>
                {categoriesList.map((category: any) => (
                  <SelectItem key={category.id} value={category.id} textValue={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Advanced Filters - Expandable */}
        {isExpanded && (
          <>
            <Separator />
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* Date Range */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <Calendar className="h-4 w-4 inline mr-2" />
                  Data Inicial
                </Label>
                <Input
                  type="date"
                  value={filters.startDate || ''}
                  onChange={(e) => updateFilter('startDate', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <Calendar className="h-4 w-4 inline mr-2" />
                  Data Final
                </Label>
                <Input
                  type="date"
                  value={filters.endDate || ''}
                  onChange={(e) => updateFilter('endDate', e.target.value)}
                />
              </div>

              {/* Amount Range */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <DollarSign className="h-4 w-4 inline mr-2" />
                  Valor Mínimo
                </Label>
                <Input
                  type="number"
                  placeholder="0.00"
                  value={filters.minAmount || ''}
                  onChange={(e) => updateFilter('minAmount', e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <DollarSign className="h-4 w-4 inline mr-2" />
                  Valor Máximo
                </Label>
                <Input
                  type="number"
                  placeholder="0.00"
                  value={filters.maxAmount || ''}
                  onChange={(e) => updateFilter('maxAmount', e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>

              {/* Family Member */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <Users className="h-4 w-4 inline mr-2" />
                  Membro da Família
                </Label>
                <Select
                  value={filters.familyMemberId || 'ALL'}
                  onValueChange={(value) => updateFilter('familyMemberId', value === 'ALL' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos os membros" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem key="ALL" value="ALL">Todos os membros</SelectItem>
                    {familyMembersList.map((member: any) => (
                      <SelectItem key={member.id} value={member.id} textValue={member.name}>
                        {member.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Tag */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <Tag className="h-4 w-4 inline mr-2" />
                  Tag
                </Label>
                <Select
                  value={filters.tagId || 'ALL'}
                  onValueChange={(value) => updateFilter('tagId', value === 'ALL' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todas as tags" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem key="ALL" value="ALL">Todas as tags</SelectItem>
                    {tagsList.map((tag: any) => (
                      <SelectItem key={tag.id} value={tag.id} textValue={tag.name}>
                        {tag.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            {/* Sorting Options */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Ordenar por</Label>
                <Select
                  value={filters.sortBy || 'transactionDate'}
                  onValueChange={(value) => updateFilter('sortBy', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SORT_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Ordem</Label>
                <Select
                  value={filters.sortOrder || 'desc'}
                  onValueChange={(value) => updateFilter('sortOrder', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SORT_ORDER_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
