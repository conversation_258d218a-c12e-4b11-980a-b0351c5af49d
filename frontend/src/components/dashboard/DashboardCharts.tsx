import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  ResponsiveContainer
} from 'recharts'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import type { DashboardOverview } from '@/types/dashboard.types'

interface DashboardChartsProps {
  overview?: DashboardOverview
  isLoading?: boolean
}

// Chart color palette
const CHART_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
]

export function DashboardCharts({ overview, isLoading }: DashboardChartsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="glass-deep shadow-elegant">
            <CardHeader>
              <div className="h-6 bg-muted rounded animate-pulse" />
              <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-64">
                <LoadingSpinner size="lg" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Prepare net worth history data
  const netWorthData = overview?.netWorth?.monthlyHistory?.map(item => ({
    month: new Date(item.month + '-01').toLocaleDateString('pt-BR', { month: 'short', year: '2-digit' }),
    netWorth: item.netWorth,
    assets: item.assets,
    liabilities: item.liabilities,
    change: item.change
  })) || []

  // Prepare expenses by category data (top 5)
  const categoryData = overview?.expensesByCategory?.categories
    ?.slice(0, 5)
    .map((category, index) => ({
      name: category.categoryName,
      value: category.amount,
      percentage: category.percentage,
      color: CHART_COLORS[index % CHART_COLORS.length]
    })) || []

  // Prepare budget comparison data
  const budgetData = overview?.budgetComparison?.categories
    ?.slice(0, 8)
    .map(category => ({
      category: category.categoryName.length > 15
        ? category.categoryName.substring(0, 15) + '...'
        : category.categoryName,
      budgeted: category.budgeted,
      spent: category.spent,
      status: category.status
    })) || []

  // Prepare account balances by type
  const accountTypeData = overview?.accountBalances?.balanceByType
    ? Object.entries(overview.accountBalances.balanceByType)
        .filter(([_, value]) => value > 0)
        .map(([type, value], index) => ({
          type: type === 'CHECKING' ? 'Conta Corrente' :
                type === 'SAVINGS' ? 'Poupança' :
                type === 'CREDIT_CARD' ? 'Cartão de Crédito' :
                type === 'INVESTMENT' ? 'Investimentos' :
                type === 'CASH' ? 'Dinheiro' : 'Outros',
          value,
          color: CHART_COLORS[index % CHART_COLORS.length]
        }))
    : []

  const chartConfig = {
    netWorth: {
      label: "Patrimônio Líquido",
      color: "hsl(var(--chart-1))",
    },
    assets: {
      label: "Ativos",
      color: "hsl(var(--chart-2))",
    },
    liabilities: {
      label: "Passivos",
      color: "hsl(var(--chart-3))",
    },
    budgeted: {
      label: "Orçado",
      color: "hsl(var(--chart-1))",
    },
    spent: {
      label: "Gasto",
      color: "hsl(var(--chart-2))",
    },
  }

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Net Worth History Chart */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground">
            Evolução do Patrimônio Líquido
          </CardTitle>
          <CardDescription>
            Histórico mensal dos últimos {netWorthData.length} meses
          </CardDescription>
        </CardHeader>
        <CardContent>
          {netWorthData.length > 0 ? (
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <LineChart data={netWorthData}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="month" 
                  className="text-muted-foreground"
                  fontSize={12}
                />
                <YAxis
                  className="text-muted-foreground"
                  fontSize={12}
                  tickFormatter={(value) => formatCurrency(value, 'BRL', true)}
                />
                <ChartTooltip 
                  content={<ChartTooltipContent 
                    formatter={(value) => [formatCurrency(Number(value)), '']}
                  />} 
                />
                <Line 
                  type="monotone" 
                  dataKey="netWorth" 
                  stroke="var(--color-netWorth)" 
                  strokeWidth={3}
                  dot={{ fill: "var(--color-netWorth)", strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: "var(--color-netWorth)", strokeWidth: 2 }}
                />
              </LineChart>
            </ChartContainer>
          ) : (
            <div className="flex items-center justify-center h-64 text-muted-foreground">
              Dados insuficientes para exibir o gráfico
            </div>
          )}
        </CardContent>
      </Card>

      {/* Expenses by Category Chart */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground">
            Gastos por Categoria
          </CardTitle>
          <CardDescription>
            Top 5 categorias do período selecionado
          </CardDescription>
        </CardHeader>
        <CardContent>
          {categoryData.length > 0 ? (
            <div className="space-y-4">
              <ChartContainer config={chartConfig} className="h-48 w-full">
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <ChartTooltip 
                    content={<ChartTooltipContent 
                      formatter={(value) => [formatCurrency(Number(value)), '']}
                    />} 
                  />
                </PieChart>
              </ChartContainer>
              
              {/* Legend */}
              <div className="grid grid-cols-1 gap-2">
                {categoryData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-foreground">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-foreground">
                        {formatCurrency(item.value)}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {item.percentage.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-muted-foreground">
              Nenhum gasto encontrado no período
            </div>
          )}
        </CardContent>
      </Card>

      {/* Budget vs Actual Chart */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground">
            Orçamento vs Real
          </CardTitle>
          <CardDescription>
            Comparação por categoria
          </CardDescription>
        </CardHeader>
        <CardContent>
          {budgetData.length > 0 ? (
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <BarChart data={budgetData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="category" 
                  className="text-muted-foreground"
                  fontSize={11}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  className="text-muted-foreground"
                  fontSize={12}
                  tickFormatter={(value) => formatCurrency(value, 'BRL', true)}
                />
                <ChartTooltip 
                  content={<ChartTooltipContent 
                    formatter={(value, name) => [
                      formatCurrency(Number(value)), 
                      name === 'budgeted' ? 'Orçado' : 'Gasto'
                    ]}
                  />} 
                />
                <Bar dataKey="budgeted" fill="var(--color-budgeted)" radius={[2, 2, 0, 0]} />
                <Bar dataKey="spent" fill="var(--color-spent)" radius={[2, 2, 0, 0]} />
              </BarChart>
            </ChartContainer>
          ) : (
            <div className="flex items-center justify-center h-64 text-muted-foreground">
              Nenhum orçamento configurado
            </div>
          )}
        </CardContent>
      </Card>

      {/* Account Balances by Type */}
      <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground">
            Saldos por Tipo de Conta
          </CardTitle>
          <CardDescription>
            Distribuição dos saldos
          </CardDescription>
        </CardHeader>
        <CardContent>
          {accountTypeData.length > 0 ? (
            <div className="space-y-4">
              <ChartContainer config={chartConfig} className="h-48 w-full">
                <PieChart>
                  <Pie
                    data={accountTypeData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {accountTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <ChartTooltip 
                    content={<ChartTooltipContent 
                      formatter={(value) => [formatCurrency(Number(value)), '']}
                    />} 
                  />
                </PieChart>
              </ChartContainer>
              
              {/* Legend */}
              <div className="grid grid-cols-1 gap-2">
                {accountTypeData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="text-foreground">{item.type}</span>
                    </div>
                    <span className="font-medium text-foreground">
                      {formatCurrency(item.value)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-muted-foreground">
              Nenhuma conta encontrada
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
