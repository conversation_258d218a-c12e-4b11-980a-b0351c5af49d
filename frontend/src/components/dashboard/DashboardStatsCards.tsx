import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  CreditCard,
  Target,
  PiggyBank,
  AlertTriangle,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { formatCurrency, formatNumber } from '@/lib/utils'
import type { DashboardOverview } from '@/types/dashboard.types'

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  color: string
  textColor: string
  description?: string
  trend?: {
    value: number
    isPositive: boolean
    label: string
  }
  isLoading?: boolean
}

function StatCard({ 
  title, 
  value, 
  icon: Icon, 
  color, 
  textColor, 
  description, 
  trend,
  isLoading = false 
}: StatCardProps) {
  if (isLoading) {
    return (
      <Card className="glass-deep shadow-elegant">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle className="text-sm font-semibold text-foreground">{title}</CardTitle>
          <div className={`flex h-10 w-10 items-center justify-center rounded-xl ${color} shadow-soft`}>
            <Icon className="h-5 w-5 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-12">
            <LoadingSpinner size="sm" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-deep shadow-elegant hover:shadow-glow transition-all duration-300">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-sm font-semibold text-foreground">{title}</CardTitle>
        <div className={`flex h-10 w-10 items-center justify-center rounded-xl ${color} shadow-soft`}>
          <Icon className="h-5 w-5 text-white" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className={`text-3xl font-bold ${textColor}`}>
            {typeof value === 'number' ? formatNumber(value) : value}
          </div>
          
          {trend && (
            <div className="flex items-center gap-2">
              {trend.isPositive ? (
                <ArrowUpRight className="h-4 w-4 text-success" />
              ) : (
                <ArrowDownRight className="h-4 w-4 text-destructive" />
              )}
              <span className={`text-sm font-medium ${trend.isPositive ? 'text-success' : 'text-destructive'}`}>
                {Math.abs(trend.value)}%
              </span>
              <span className="text-sm text-muted-foreground">
                {trend.label}
              </span>
            </div>
          )}
          
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface DashboardStatsCardsProps {
  overview?: DashboardOverview
  isLoading?: boolean
}

export function DashboardStatsCards({ overview, isLoading }: DashboardStatsCardsProps) {
  // Calculate monthly change for net worth trend
  const getNetWorthTrend = () => {
    if (!overview?.netWorth?.monthlyHistory || overview.netWorth.monthlyHistory.length < 2) {
      return undefined
    }

    const history = overview.netWorth.monthlyHistory
    const latest = history[history.length - 1]
    return {
      value: Math.abs(latest.changePercentage),
      isPositive: latest.changePercentage >= 0,
      label: 'vs mês anterior'
    }
  }

  // Calculate budget utilization
  const getBudgetUtilization = () => {
    if (!overview?.budgetComparison?.totalBudgeted) return 0
    return (overview.budgetComparison.totalSpent / overview.budgetComparison.totalBudgeted) * 100
  }

  // Calculate credit card utilization status
  const getCreditCardStatus = () => {
    const utilization = overview?.creditCardUsage?.usagePercentage || 0
    if (utilization >= 80) return 'danger'
    if (utilization >= 60) return 'warning'
    return 'safe'
  }

  const stats = [
    {
      title: 'Patrimônio Líquido',
      value: formatCurrency(overview?.netWorth?.currentNetWorth || 0),
      icon: PiggyBank,
      color: 'bg-gradient-deep',
      textColor: 'text-foreground',
      description: 'Ativos - Passivos',
      trend: getNetWorthTrend()
    },
    {
      title: 'Saldo Total',
      value: formatCurrency(overview?.accountBalances?.totalBalance || 0),
      icon: DollarSign,
      color: 'bg-success',
      textColor: 'text-foreground',
      description: 'Soma de todas as contas'
    },
    {
      title: 'Gastos do Período',
      value: formatCurrency(overview?.expensesByCategory?.totalExpenses || 0),
      icon: TrendingDown,
      color: 'bg-destructive',
      textColor: 'text-foreground',
      description: 'Total de despesas'
    },
    {
      title: 'Uso do Cartão',
      value: `${(overview?.creditCardUsage?.usagePercentage || 0).toFixed(1)}%`,
      icon: CreditCard,
      color: getCreditCardStatus() === 'danger' ? 'bg-destructive' :
             getCreditCardStatus() === 'warning' ? 'bg-warning' : 'bg-success',
      textColor: 'text-foreground',
      description: `${formatCurrency(overview?.creditCardUsage?.totalUsage || 0)} de ${formatCurrency(overview?.creditCardUsage?.totalLimit || 0)}`
    },
    {
      title: 'Orçamento Utilizado',
      value: `${getBudgetUtilization().toFixed(1)}%`,
      icon: Target,
      color: getBudgetUtilization() > 100 ? 'bg-destructive' :
             getBudgetUtilization() > 80 ? 'bg-warning' : 'bg-success',
      textColor: 'text-foreground',
      description: `${formatCurrency(overview?.budgetComparison?.totalSpent || 0)} de ${formatCurrency(overview?.budgetComparison?.totalBudgeted || 0)}`
    },
    {
      title: 'Metas Ativas',
      value: overview?.goalProgress?.activeGoals || 0,
      icon: Target,
      color: 'bg-blue-600',
      textColor: 'text-foreground',
      description: `${(overview?.goalProgress?.summary?.overallProgress || 0).toFixed(1)}% de progresso geral`
    }
  ]

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {stats.map((stat) => (
        <StatCard
          key={stat.title}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
          color={stat.color}
          textColor={stat.textColor}
          description={stat.description}
          trend={stat.trend}
          isLoading={isLoading}
        />
      ))}
    </div>
  )
}
