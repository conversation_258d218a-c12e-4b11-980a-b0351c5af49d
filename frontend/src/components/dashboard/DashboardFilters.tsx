import { useState } from 'react'
import { 
  Calendar,
  Filter,
  X,
  ChevronDown,
  Users,
  CreditCard,
  Tag
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import type { DashboardFilters } from '@/types/dashboard.types'

export interface DashboardFiltersProps {
  filters: DashboardFilters
  onFiltersChange: (filters: DashboardFilters) => void
  onClearFilters: () => void
  isLoading?: boolean
}

// Predefined period options
const PERIOD_OPTIONS = [
  { value: 'current-month', label: 'M<PERSON>s Atual', month: new Date().getMonth() + 1, year: new Date().getFullYear() },
  { value: 'last-month', label: 'Mês Passado', month: new Date().getMonth() === 0 ? 12 : new Date().getMonth(), year: new Date().getMonth() === 0 ? new Date().getFullYear() - 1 : new Date().getFullYear() },
  { value: 'last-3-months', label: 'Últimos 3 Meses' },
  { value: 'last-6-months', label: 'Últimos 6 Meses' },
  { value: 'current-year', label: 'Ano Atual', year: new Date().getFullYear() },
  { value: 'last-year', label: 'Ano Passado', year: new Date().getFullYear() - 1 },
  { value: 'all-time', label: 'Todo o Período' },
]

export function DashboardFilters({ 
  filters, 
  onFiltersChange, 
  onClearFilters, 
  isLoading = false 
}: DashboardFiltersProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)

  const updateFilter = (key: keyof DashboardFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    })
  }

  const handlePeriodChange = (periodValue: string) => {
    const period = PERIOD_OPTIONS.find(p => p.value === periodValue)
    if (!period) return

    let newFilters: DashboardFilters = { ...filters }

    // Clear existing date/period filters
    delete newFilters.startDate
    delete newFilters.endDate
    delete newFilters.month
    delete newFilters.year

    if (period.value === 'current-month' || period.value === 'last-month') {
      newFilters.month = period.month
      newFilters.year = period.year
    } else if (period.value === 'current-year' || period.value === 'last-year') {
      newFilters.year = period.year
    } else if (period.value === 'last-3-months') {
      const date = new Date()
      date.setMonth(date.getMonth() - 3)
      newFilters.startDate = date.toISOString().split('T')[0]
      newFilters.endDate = new Date().toISOString().split('T')[0]
    } else if (period.value === 'last-6-months') {
      const date = new Date()
      date.setMonth(date.getMonth() - 6)
      newFilters.startDate = date.toISOString().split('T')[0]
      newFilters.endDate = new Date().toISOString().split('T')[0]
    }
    // For 'all-time', we don't set any filters

    onFiltersChange(newFilters)
  }

  const getCurrentPeriodValue = () => {
    if (filters.month && filters.year) {
      const currentMonth = new Date().getMonth() + 1
      const currentYear = new Date().getFullYear()
      
      if (filters.month === currentMonth && filters.year === currentYear) {
        return 'current-month'
      }
      
      const lastMonth = currentMonth === 1 ? 12 : currentMonth - 1
      const lastMonthYear = currentMonth === 1 ? currentYear - 1 : currentYear
      
      if (filters.month === lastMonth && filters.year === lastMonthYear) {
        return 'last-month'
      }
    }
    
    if (filters.year && !filters.month) {
      const currentYear = new Date().getFullYear()
      if (filters.year === currentYear) return 'current-year'
      if (filters.year === currentYear - 1) return 'last-year'
    }
    
    if (filters.startDate && filters.endDate) {
      const start = new Date(filters.startDate)
      const end = new Date(filters.endDate)
      const now = new Date()
      
      const diffMonths = (now.getFullYear() - start.getFullYear()) * 12 + (now.getMonth() - start.getMonth())
      
      if (diffMonths >= 2 && diffMonths <= 4) return 'last-3-months'
      if (diffMonths >= 5 && diffMonths <= 7) return 'last-6-months'
    }
    
    if (!filters.startDate && !filters.endDate && !filters.month && !filters.year) {
      return 'all-time'
    }
    
    return 'custom'
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.accountIds?.length) count++
    if (filters.categoryIds?.length) count++
    if (filters.familyMemberIds?.length) count++
    if (filters.currencies?.length) count++
    if (filters.tagIds?.length) count++
    if (filters.budgetIds?.length) count++
    return count
  }

  const hasActiveFilters = () => {
    return getActiveFiltersCount() > 0 || 
           filters.startDate || 
           filters.endDate || 
           filters.month || 
           filters.year
  }

  return (
    <Card className="glass-deep shadow-elegant">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros do Dashboard
          </CardTitle>
          {hasActiveFilters() && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4 mr-1" />
              Limpar
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Period Filter - Always Visible */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Período
          </label>
          <Select
            value={getCurrentPeriodValue()}
            onValueChange={handlePeriodChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Selecione o período" />
            </SelectTrigger>
            <SelectContent>
              {PERIOD_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Advanced Filters - Collapsible */}
        <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-between text-muted-foreground hover:text-foreground"
            >
              <span className="flex items-center gap-2">
                Filtros Avançados
                {getActiveFiltersCount() > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {getActiveFiltersCount()}
                  </Badge>
                )}
              </span>
              <ChevronDown className={`h-4 w-4 transition-transform ${isAdvancedOpen ? 'rotate-180' : ''}`} />
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="space-y-4 pt-4">
            <Separator />
            
            {/* Note: Advanced filters would be implemented here */}
            {/* For now, showing placeholder for future implementation */}
            <div className="text-sm text-muted-foreground text-center py-4">
              Filtros avançados por contas, categorias, membros da família, etc. serão implementados em breve.
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  )
}
