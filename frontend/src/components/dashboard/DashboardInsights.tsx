import { 
  <PERSON>ert<PERSON><PERSON>gle, 
  CheckCircle, 
  Info, 
  TrendingUp, 
  TrendingDown,
  Target,
  CreditCard,
  Calendar
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { formatCurrency } from '@/lib/utils'
import type { DashboardOverview } from '@/types/dashboard.types'

interface DashboardInsightsProps {
  overview?: DashboardOverview
  isLoading?: boolean
}

export function DashboardInsights({ overview, isLoading }: DashboardInsightsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        {[1, 2].map((i) => (
          <Card key={i} className="glass-deep shadow-elegant">
            <CardHeader>
              <div className="h-6 bg-muted rounded animate-pulse" />
              <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[1, 2, 3].map((j) => (
                  <div key={j} className="h-16 bg-muted rounded animate-pulse" />
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Generate budget alerts
  const budgetAlerts = overview?.budgetComparison?.alerts || []

  // Generate credit card alerts
  const creditCardAlerts = overview?.creditCardUsage?.cardDetails
    ?.filter(card => card.usagePercentage >= 80)
    .map(card => ({
      type: 'credit-card' as const,
      severity: card.usagePercentage >= 90 ? 'HIGH' as const : 'MEDIUM' as const,
      title: `Cartão ${card.accountName}`,
      message: `Utilização de ${card.usagePercentage.toFixed(1)}% do limite`,
      value: card.usagePercentage
    })) || []

  // Generate goal insights
  const goalInsights = overview?.goalProgress?.goals
    ?.filter(goal => goal.status === 'IN_PROGRESS')
    .slice(0, 3)
    .map(goal => ({
      id: goal.goalId,
      name: goal.goalName,
      progress: goal.progress,
      isOnTrack: goal.isOnTrack,
      daysRemaining: goal.daysRemaining,
      currentAmount: goal.currentAmount,
      targetAmount: goal.targetAmount
    })) || []

  // Calculate net worth trend
  const netWorthTrend = (() => {
    const history = overview?.netWorth?.monthlyHistory
    if (!history || history.length < 2) return null

    const latest = history[history.length - 1]
    const previous = history[history.length - 2]

    return {
      current: latest.netWorth,
      previous: previous.netWorth,
      change: latest.change,
      changePercentage: latest.changePercentage,
      isPositive: latest.changePercentage >= 0
    }
  })()

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Alerts and Warnings */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Alertas e Avisos
          </CardTitle>
          <CardDescription>
            Situações que requerem atenção
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Budget Alerts */}
          {budgetAlerts.map((alert, index) => (
            <Alert 
              key={`budget-${index}`}
              variant={alert.severity === 'HIGH' ? 'destructive' : 'default'}
              className="border-l-4"
            >
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium">{alert.categoryName}</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      {alert.message}
                    </p>
                  </div>
                  <Badge 
                    variant={alert.severity === 'HIGH' ? 'destructive' : 'secondary'}
                    className="ml-2"
                  >
                    {alert.alertType === 'OVER_BUDGET' ? 'Estourado' : 
                     alert.alertType === 'NEAR_BUDGET' ? 'Próximo do limite' : 'Projeção'}
                  </Badge>
                </div>
              </AlertDescription>
            </Alert>
          ))}

          {/* Credit Card Alerts */}
          {creditCardAlerts.map((alert, index) => (
            <Alert 
              key={`card-${index}`}
              variant={alert.severity === 'HIGH' ? 'destructive' : 'default'}
              className="border-l-4"
            >
              <CreditCard className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="font-medium">{alert.title}</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      {alert.message}
                    </p>
                  </div>
                  <Badge 
                    variant={alert.severity === 'HIGH' ? 'destructive' : 'secondary'}
                    className="ml-2"
                  >
                    {alert.value >= 90 ? 'Crítico' : 'Atenção'}
                  </Badge>
                </div>
              </AlertDescription>
            </Alert>
          ))}

          {/* No Alerts */}
          {budgetAlerts.length === 0 && creditCardAlerts.length === 0 && (
            <Alert className="border-l-4 border-l-success">
              <CheckCircle className="h-4 w-4 text-success" />
              <AlertDescription>
                <span className="font-medium text-success">Tudo em ordem!</span>
                <p className="text-sm text-muted-foreground mt-1">
                  Não há alertas ou avisos no momento.
                </p>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Insights and Progress */}
      <Card className="glass-deep shadow-elegant">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Insights e Progresso
          </CardTitle>
          <CardDescription>
            Análises e acompanhamento de metas
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Net Worth Trend */}
          {netWorthTrend && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-foreground">Patrimônio Líquido</span>
                <div className="flex items-center gap-2">
                  {netWorthTrend.isPositive ? (
                    <TrendingUp className="h-4 w-4 text-success" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-destructive" />
                  )}
                  <span className={`text-sm font-medium ${netWorthTrend.isPositive ? 'text-success' : 'text-destructive'}`}>
                    {netWorthTrend.isPositive ? '+' : ''}{netWorthTrend.changePercentage.toFixed(1)}%
                  </span>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                {netWorthTrend.isPositive ? 'Crescimento' : 'Redução'} de {formatCurrency(Math.abs(netWorthTrend.change))} no último mês
              </p>
            </div>
          )}

          {/* Goal Progress */}
          {goalInsights.length > 0 && (
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
                <Target className="h-4 w-4" />
                Progresso das Metas
              </h4>
              
              {goalInsights.map((goal) => (
                <div key={goal.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-foreground">
                      {goal.name}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {goal.progress.toFixed(1)}%
                      </span>
                      {goal.isOnTrack ? (
                        <CheckCircle className="h-4 w-4 text-success" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-warning" />
                      )}
                    </div>
                  </div>
                  
                  <Progress value={goal.progress} className="h-2" />
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>
                      {formatCurrency(goal.currentAmount)} de {formatCurrency(goal.targetAmount)}
                    </span>
                    {goal.daysRemaining && (
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {goal.daysRemaining} dias restantes
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Summary Insight */}
          <div className="pt-4 border-t border-border">
            <Alert className="border-l-4 border-l-blue-500">
              <Info className="h-4 w-4 text-blue-500" />
              <AlertDescription>
                <span className="font-medium text-blue-600">Resumo do Período</span>
                <p className="text-sm text-muted-foreground mt-1">
                  {overview?.expensesByCategory?.totalExpenses ? (
                    <>
                      Total de gastos: {formatCurrency(overview.expensesByCategory.totalExpenses)}
                      {overview?.budgetComparison?.totalBudgeted && overview.budgetComparison.totalBudgeted > 0 && (
                        <> • {((overview.expensesByCategory.totalExpenses / overview.budgetComparison.totalBudgeted) * 100).toFixed(1)}% do orçamento utilizado</>
                      )}
                    </>
                  ) : (
                    'Nenhum gasto registrado no período selecionado.'
                  )}
                </p>
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
