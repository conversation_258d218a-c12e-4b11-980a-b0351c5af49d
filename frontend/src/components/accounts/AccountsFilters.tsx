import { useState } from 'react'
import { 
  Filter,
  X,
  Search,
  Eye,
  EyeOff,
  Archive,
  CreditCard,
  PiggyBank,
  TrendingUp,
  Wallet,
  Banknote
} from 'lucide-react'
import { ACCOUNT_TYPES, CURRENCIES } from '@/lib/constants'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export interface AccountFilters {
  search?: string
  type?: keyof typeof ACCOUNT_TYPES | 'ALL'
  currency?: string | 'ALL'
  includeInTotal?: boolean | 'ALL'
  archived?: boolean | 'ALL'
  sortBy?: 'name' | 'balance' | 'type' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

interface AccountsFiltersProps {
  filters: AccountFilters
  onFiltersChange: (filters: AccountFilters) => void
  onClearFilters: () => void
}

const accountTypeIcons = {
  CHECKING: Wallet,
  SAVINGS: PiggyBank,
  CREDIT_CARD: CreditCard,
  INVESTMENT: TrendingUp,
  CASH: Banknote,
  ASSETS: TrendingUp,
}

const accountTypeLabels = {
  CHECKING: 'Conta Corrente',
  SAVINGS: 'Poupança',
  CREDIT_CARD: 'Cartão de Crédito',
  INVESTMENT: 'Investimento',
  CASH: 'Dinheiro',
  ASSETS: 'Ativos',
}

const sortOptions = [
  { value: 'name', label: 'Nome' },
  { value: 'balance', label: 'Saldo' },
  { value: 'type', label: 'Tipo' },
  { value: 'createdAt', label: 'Data de Criação' },
]

export function AccountsFilters({ filters, onFiltersChange, onClearFilters }: AccountsFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const updateFilter = (key: keyof AccountFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== 'ALL' && value !== ''
  )

  return (
    <div className="card">
      {/* Header com busca e toggle de filtros */}
      <div className="flex items-center gap-4 mb-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-500" />
          <Input
            placeholder="Buscar contas..."
            value={filters.search || ''}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className={`btn-outline ${hasActiveFilters ? 'border-primary text-primary' : ''}`}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filtros
          {hasActiveFilters && (
            <span className="ml-2 bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs">
              {Object.values(filters).filter(v => v !== undefined && v !== 'ALL' && v !== '').length}
            </span>
          )}
        </Button>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-secondary-400 hover:text-white"
          >
            <X className="h-4 w-4 mr-2" />
            Limpar
          </Button>
        )}
      </div>

      {/* Filtros expandidos */}
      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-border">
          {/* Tipo de Conta */}
          <div className="form-group">
            <Label className="form-label">Tipo de Conta</Label>
            <Select
              value={filters.type || 'ALL'}
              onValueChange={(value) => updateFilter('type', value === 'ALL' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Todos os tipos</SelectItem>
                {Object.entries(ACCOUNT_TYPES).map(([key]) => {
                  return (
                    <SelectItem key={key} value={key} textValue={accountTypeLabels[key as keyof typeof accountTypeLabels]}>
                      {accountTypeLabels[key as keyof typeof accountTypeLabels]}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Moeda */}
          <div className="form-group">
            <Label className="form-label">Moeda</Label>
            <Select
              value={filters.currency || 'ALL'}
              onValueChange={(value) => updateFilter('currency', value === 'ALL' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem key="ALL" value="ALL">Todas as moedas</SelectItem>
                {CURRENCIES.map((currency) => (
                  <SelectItem key={currency.code} value={currency.code} textValue={`${currency.code} - ${currency.name}`}>
                    {currency.code} - {currency.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Incluir no Total */}
          <div className="form-group">
            <Label className="form-label">Visibilidade</Label>
            <Select
              value={filters.includeInTotal?.toString() || 'ALL'}
              onValueChange={(value) => updateFilter('includeInTotal', value === 'ALL' ? undefined : value === 'true')}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem key="ALL" value="ALL">Todas</SelectItem>
                <SelectItem key="true" value="true" textValue="Incluídas no total">
                  👁️ Incluídas no total
                </SelectItem>
                <SelectItem key="false" value="false" textValue="Ocultas do total">
                  🙈 Ocultas do total
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status (Arquivada) */}
          <div className="form-group">
            <Label className="form-label">Status</Label>
            <Select
              value={filters.archived?.toString() || 'ALL'}
              onValueChange={(value) => updateFilter('archived', value === 'ALL' ? undefined : value === 'true')}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem key="ALL" value="ALL">Todas</SelectItem>
                <SelectItem key="false" value="false" textValue="Ativas">
                  🟢 Ativas
                </SelectItem>
                <SelectItem key="true" value="true" textValue="Arquivadas">
                  📦 Arquivadas
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Ordenação */}
      <div className="flex items-center gap-4 mt-4 pt-4 border-t border-border">
        <Label className="form-label text-sm">Ordenar por:</Label>
        
        <Select
          value={filters.sortBy || 'name'}
          onValueChange={(value) => updateFilter('sortBy', value)}
        >
          <SelectTrigger className="w-auto">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
          className="flex items-center gap-2"
        >
          {filters.sortOrder === 'desc' ? '↓' : '↑'}
          {filters.sortOrder === 'desc' ? 'Decrescente' : 'Crescente'}
        </Button>
      </div>
    </div>
  )
}
