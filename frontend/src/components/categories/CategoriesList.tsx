import React, { useState, useMemo } from 'react'
import {
  MoreHorizontal,
  Edit,
  Archive,
  Trash2,
  FolderOpen,
  Folder,
  Tags,
  ArchiveRestore,
  Search,
  Filter,
  Grid3X3,
  List,
  ChevronDown,
  ChevronRight,
  Plus
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { useCategories, useArchiveCategory, useDeleteCategory } from '@/hooks/useCategories'
import type { Category } from '@/types/category.types'
import { formatDate } from '@/lib/utils'

interface CategoriesListProps {
  onEdit: (category: Category) => void
  onCreateSubcategory?: (parentId: string) => void
}

type ViewMode = 'table' | 'cards'

export function CategoriesList({ onEdit, onCreateSubcategory }: CategoriesListProps) {
  const [includeArchived, setIncludeArchived] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  
  const { data: categoriesData, isLoading, error } = useCategories({
    includeArchived,
    includeChildren: true,
    page: 1,
    limit: 100
  })

  const archiveMutation = useArchiveCategory()
  const deleteMutation = useDeleteCategory()

  const categories = categoriesData?.data || []

  // Filter categories based on search term
  const filteredCategories = useMemo(() => {
    if (!searchTerm) return categories

    return categories.filter(category =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.parent?.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [categories, searchTerm])

  const toggleCategoryExpansion = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const handleArchive = async (category: Category) => {
    try {
      await archiveMutation.mutateAsync({
        id: category.id,
        archived: !category.deletedAt
      })
    } catch (error) {
      console.error('Error archiving category:', error)
    }
  }

  const handleDelete = async (category: Category) => {
    if (window.confirm(`Tem certeza que deseja excluir a categoria "${category.name}"? Esta ação não pode ser desfeita.`)) {
      try {
        await deleteMutation.mutateAsync(category.id)
      } catch (error) {
        console.error('Error deleting category:', error)
      }
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-32 items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="py-12 text-center">
        <Tags className="mx-auto mb-4 h-12 w-12 text-destructive" />
        <h3 className="mb-2 text-lg font-medium text-foreground">
          Erro ao carregar categorias
        </h3>
        <p className="text-muted-foreground">
          Ocorreu um erro ao carregar a lista de categorias.
        </p>
      </div>
    )
  }

  if (!categories || categories.length === 0) {
    return (
      <div className="py-12 text-center">
        <Tags className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
        <h3 className="mb-2 text-lg font-medium text-foreground">
          {includeArchived ? 'Nenhuma categoria encontrada' : 'Nenhuma categoria ativa'}
        </h3>
        <p className="text-muted-foreground">
          {includeArchived
            ? 'Não há categorias cadastradas no sistema.'
            : 'Comece criando sua primeira categoria.'
          }
        </p>
      </div>
    )
  }

  // Organize categories hierarchically
  const parentCategories = filteredCategories.filter(cat => !cat.parentId)
  const childCategories = filteredCategories.filter(cat => cat.parentId)

  const getCategoryChildren = (parentId: string) => {
    return childCategories.filter(cat => cat.parentId === parentId)
  }

  const renderCategoryCard = (category: Category, level = 0) => {
    const isArchived = !!category.deletedAt
    const children = getCategoryChildren(category.id)
    const isExpanded = expandedCategories.has(category.id)

    return (
      <div key={category.id} className={`${level > 0 ? 'ml-6' : ''}`}>
        <Card className={`glass-deep shadow-elegant hover:shadow-glow transition-all duration-300 ${isArchived ? 'opacity-60' : ''}`}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {children.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => toggleCategoryExpansion(category.id)}
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                )}

                {category.color && (
                  <div
                    className="h-6 w-6 rounded-full border-2 border-border shadow-soft"
                    style={{ backgroundColor: category.color }}
                  />
                )}

                {level === 0 ? (
                  <Folder className="h-5 w-5 text-primary" />
                ) : (
                  <FolderOpen className="h-5 w-5 text-muted-foreground" />
                )}

                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    {category.name}
                  </CardTitle>
                  {category.parentId && (
                    <p className="text-sm text-muted-foreground mt-1">
                      Subcategoria de {categories.find(c => c.id === category.parentId)?.name || 'Categoria não encontrada'}
                    </p>
                  )}
                </div>

                {isArchived && (
                  <Badge variant="secondary" className="ml-2">
                    Arquivada
                  </Badge>
                )}
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(category)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Editar
                  </DropdownMenuItem>

                  {!category.parentId && onCreateSubcategory && (
                    <DropdownMenuItem onClick={() => onCreateSubcategory(category.id)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Adicionar Subcategoria
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  <DropdownMenuItem
                    onClick={() => handleArchive(category)}
                    disabled={archiveMutation.isPending}
                  >
                    {isArchived ? (
                      <>
                        <ArchiveRestore className="mr-2 h-4 w-4" />
                        Restaurar
                      </>
                    ) : (
                      <>
                        <Archive className="mr-2 h-4 w-4" />
                        Arquivar
                      </>
                    )}
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => handleDelete(category)}
                    disabled={deleteMutation.isPending}
                    className="text-error-400 focus:text-error-400"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Excluir
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-4">
                <span>{category.transactionCount || 0} transações</span>
                <span>{category.budgetCount || 0} orçamentos</span>
              </div>
              <span>{formatDate(category.createdAt)}</span>
            </div>
          </CardContent>
        </Card>

        {/* Render children */}
        {children.length > 0 && isExpanded && (
          <div className="mt-4 space-y-3">
            {children.map(child => renderCategoryCard(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  const renderCategoryRow = (category: Category, level = 0) => {
    const isArchived = !!category.deletedAt
    const children = getCategoryChildren(category.id)

    return (
      <React.Fragment key={category.id}>
        <TableRow className={isArchived ? 'opacity-60' : ''}>
          <TableCell>
            <div className="flex items-center gap-2" style={{ paddingLeft: `${level * 20}px` }}>
              {level === 0 ? (
                <Folder className="h-4 w-4 text-primary" />
              ) : (
                <FolderOpen className="h-4 w-4 text-muted-foreground" />
              )}
              <span className="font-medium text-foreground">{category.name}</span>
              {isArchived && (
                <Badge variant="secondary" className="ml-2">
                  Arquivada
                </Badge>
              )}
            </div>
          </TableCell>

          <TableCell>
            {category.color && (
              <div className="flex items-center gap-2">
                <div
                  className="h-5 w-5 rounded-full border-2 border-border shadow-soft"
                  style={{ backgroundColor: category.color }}
                />
                <span className="text-sm text-muted-foreground">{category.color}</span>
              </div>
            )}
          </TableCell>

          <TableCell>
            <div className="text-sm text-muted-foreground">
              {category.parentId ? (
                <div className="flex items-center gap-1">
                  <Folder className="h-3 w-3" />
                  {categories.find(c => c.id === category.parentId)?.name || 'Categoria não encontrada'}
                </div>
              ) : (
                <span>Categoria principal</span>
              )}
            </div>
          </TableCell>

          <TableCell>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>{category.transactionCount || 0} transações</span>
              <span>{category.budgetCount || 0} orçamentos</span>
            </div>
          </TableCell>

          <TableCell>
            <span className="text-sm text-muted-foreground">
              {formatDate(category.createdAt)}
            </span>
          </TableCell>

          <TableCell>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(category)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Editar
                </DropdownMenuItem>

                {!category.parentId && onCreateSubcategory && (
                  <DropdownMenuItem onClick={() => onCreateSubcategory(category.id)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Adicionar Subcategoria
                  </DropdownMenuItem>
                )}

                <DropdownMenuSeparator />

                <DropdownMenuItem
                  onClick={() => handleArchive(category)}
                  disabled={archiveMutation.isPending}
                >
                  {isArchived ? (
                    <>
                      <ArchiveRestore className="mr-2 h-4 w-4" />
                      Restaurar
                    </>
                  ) : (
                    <>
                      <Archive className="mr-2 h-4 w-4" />
                      Arquivar
                    </>
                  )}
                </DropdownMenuItem>

                <DropdownMenuItem
                  onClick={() => handleDelete(category)}
                  disabled={deleteMutation.isPending}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TableCell>
        </TableRow>

        {/* Render children */}
        {children.map(child => renderCategoryRow(child, level + 1))}
      </React.Fragment>
    )
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar categorias..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          {/* Archive Filter */}
          <Button
            variant={includeArchived ? "default" : "outline"}
            size="sm"
            onClick={() => setIncludeArchived(!includeArchived)}
            className="shrink-0"
          >
            <Archive className="mr-2 h-4 w-4" />
            {includeArchived ? 'Ocultar Arquivadas' : 'Mostrar Arquivadas'}
          </Button>
        </div>

        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex items-center gap-1 rounded-lg border border-border p-1">
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
              className="h-8 px-3"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="h-8 px-3"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
          </div>

          {/* Results Count */}
          <div className="text-sm text-muted-foreground shrink-0">
            {filteredCategories.length} categoria{filteredCategories.length !== 1 ? 's' : ''} encontrada{filteredCategories.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>

      {/* Content */}
      {viewMode === 'cards' ? (
        <div className="space-y-4">
          {parentCategories.map(category => renderCategoryCard(category))}
        </div>
      ) : (
        <div className="glass-deep rounded-2xl shadow-elegant overflow-hidden">
          <Table>
            <TableHeader className="border-border/50">
              <TableRow className="hover:bg-accent/30">
                <TableHead className="font-semibold text-foreground">Nome</TableHead>
                <TableHead className="font-semibold text-foreground">Cor</TableHead>
                <TableHead className="font-semibold text-foreground">Categoria Pai</TableHead>
                <TableHead className="font-semibold text-foreground">Uso</TableHead>
                <TableHead className="font-semibold text-foreground">Criada em</TableHead>
                <TableHead className="w-[70px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {parentCategories.map(category => renderCategoryRow(category))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
