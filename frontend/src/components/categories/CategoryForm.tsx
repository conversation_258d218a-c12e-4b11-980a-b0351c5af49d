"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON>ader2, <PERSON>older, FolderOpen } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  categoryFormSchema,
  type CategoryFormData,
} from "@/lib/validations/category"
import { CATEGORY_COLORS, type Category } from "@/types/category.types"
import { CategoryColorPicker } from "./CategoryColorPicker"
import { useCategoriesForSelection } from "@/hooks/useCategories"

interface CategoryFormProps {
  category?: Category
  onSubmit: (data: CategoryFormData) => void
  isLoading?: boolean
  onCancel?: () => void
}

export function CategoryForm({
  category,
  onSubmit,
  isLoading = false,
  onCancel,
}: CategoryFormProps) {
  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: category?.name || "",
      color: category?.color || CATEGORY_COLORS[0],
      parentId: category?.parentId || undefined,
    },
  })

  // Fetch categories for parent selection, excluding current category to prevent loops
  const { data: rawParentOptions = [], isLoading: isLoadingParents } = useCategoriesForSelection(
    category?.id
  )

  // Ensure data integrity and remove duplicates
  const parentOptions = rawParentOptions
    .filter((option, index, array) =>
      option &&
      option.value &&
      option.label &&
      array.findIndex(item => item.value === option.value) === index
    )

  const handleFormSubmit = (data: CategoryFormData) => {
    onSubmit(data)
  }

  const watchedColor = form.watch("color")
  const watchedParentId = form.watch("parentId")

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Name Field */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome da Categoria</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Digite o nome da categoria" 
                  {...field} 
                  className="bg-background"
                />
              </FormControl>
              <FormDescription>
                Nome da categoria (2-100 caracteres)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Parent Category Selection */}
        <FormField
          control={form.control}
          name="parentId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Categoria Pai (Opcional)</FormLabel>
              <Select
                onValueChange={(value) => {
                  // Convert "__none__" back to undefined for the form
                  field.onChange(value === "__none__" ? undefined : value)
                }}
                value={field.value || "__none__"}
                disabled={isLoadingParents}
              >
                <FormControl>
                  <SelectTrigger className="bg-background">
                    <SelectValue placeholder="Selecione uma categoria pai (opcional)">
                      {field.value ? (
                        <div className="flex items-center gap-2">
                          <FolderOpen className="h-4 w-4" />
                          {parentOptions.find(opt => opt.value === field.value)?.label}
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Folder className="h-4 w-4" />
                          Categoria principal (sem pai)
                        </div>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem key="none-option" value="__none__">
                    📁 Categoria principal (sem pai)
                  </SelectItem>
                  {Array.isArray(parentOptions) && parentOptions.map((option, index) => {
                    if (!option || !option.value || !option.label) return null
                    return (
                      <SelectItem
                        key={`parent-${option.value}-${index}`}
                        value={option.value}
                        disabled={option.disabled}
                        textValue={option.label}
                      >
                        📂 {option.label}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
              <FormDescription>
                {watchedParentId 
                  ? "Esta será uma subcategoria" 
                  : "Esta será uma categoria principal"
                }
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Color Picker */}
        <FormField
          control={form.control}
          name="color"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cor da Categoria (Opcional)</FormLabel>
              <FormControl>
                <CategoryColorPicker
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormDescription>
                Escolha uma cor para identificar esta categoria visualmente
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Preview */}
        {(form.watch("name") || watchedColor) && (
          <div className="rounded-lg border bg-muted/50 p-4">
            <FormLabel className="text-sm font-medium">Preview</FormLabel>
            <div className="mt-2 flex items-center gap-3">
              <div
                className="h-6 w-6 rounded-full border-2 border-background shadow-sm"
                style={{ backgroundColor: watchedColor || CATEGORY_COLORS[0] }}
              />
              <div className="flex items-center gap-2">
                {watchedParentId ? (
                  <>
                    <FolderOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {parentOptions.find(opt => opt.value === watchedParentId)?.label}
                    </span>
                    <span className="text-muted-foreground">/</span>
                  </>
                ) : (
                  <Folder className="h-4 w-4 text-muted-foreground" />
                )}
                <span className="font-medium">
                  {form.watch("name") || "Nome da categoria"}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-2 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          )}
          <Button type="submit" disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {category ? "Atualizar" : "Criar"} Categoria
          </Button>
        </div>
      </form>
    </Form>
  )
}
